"""
人群生成器模块

实现PopulationGenerator类，用于根据配置参数生成具有指定
人口统计学特征的个体人群。
"""

import uuid
from typing import Dict, List, Optional, Union, Any, Tuple
from dataclasses import dataclass
import numpy as np
import pandas as pd
from pathlib import Path
from tqdm import tqdm

from ...core import Individual, Population, Gender, DiseaseState, PathwayType
from ...utils import (
    validate_age, validate_positive_number, validate_probability,
    ValidationError, ParameterValidationError
)


@dataclass
class GenerationSummary:
    """人群生成摘要"""
    total_individuals: int
    generation_time: float
    age_stats: Dict[str, float]
    gender_distribution: Dict[str, int]
    pathway_distribution: Dict[str, int]
    config_used: Dict[str, Any]


class PopulationGenerator:
    """
    人群生成器类
    
    根据配置参数生成具有指定人口统计学特征的个体人群，
    支持多种年龄分布、性别比例和疾病通路分配。
    """
    
    def __init__(self, random_seed: Optional[int] = None):
        """
        初始化人群生成器
        
        Args:
            random_seed: 随机种子，用于确保结果可重现
        """
        self.random_seed = random_seed
        if random_seed is not None:
            np.random.seed(random_seed)
        
        self._last_generation_summary: Optional[GenerationSummary] = None
    
    def generate_population(
        self,
        size: int,
        age_distribution: Dict[str, Any],
        gender_distribution: Dict[str, float],
        pathway_distribution: Optional[Dict[str, float]] = None,
        birth_year_base: int = 2025,
        show_progress: bool = True
    ) -> Population:
        """
        生成人群
        
        Args:
            size: 人群规模
            age_distribution: 年龄分布配置
            gender_distribution: 性别分布配置
            pathway_distribution: 疾病通路分布配置
            birth_year_base: 基准年份，用于计算出生年份
            show_progress: 是否显示进度条
            
        Returns:
            生成的人群对象
            
        Raises:
            ValidationError: 参数验证失败时
        """
        import time
        start_time = time.time()
        
        # 验证参数
        self._validate_generation_parameters(
            size, age_distribution, gender_distribution, pathway_distribution
        )
        
        # 生成年龄分布
        ages = self._generate_age_distribution(size, age_distribution)
        
        # 分配性别
        genders = self._assign_genders(size, gender_distribution)
        
        # 分配疾病通路类型
        pathway_types = self._assign_pathway_types(size, pathway_distribution)
        
        # 创建个体列表
        individuals = []
        
        # 使用进度条显示生成进度
        iterator = range(size)
        if show_progress and size > 1000:
            iterator = tqdm(iterator, desc="生成个体", unit="个")
        
        for i in iterator:
            birth_year = birth_year_base - int(ages[i])
            individual = Individual(
                birth_year=birth_year,
                gender=genders[i],
                individual_id=str(uuid.uuid4()),
                pathway_type=pathway_types[i] if pathway_types else None
            )
            individuals.append(individual)
        
        # 创建人群对象
        population = Population(individuals)
        
        # 生成摘要
        generation_time = time.time() - start_time
        self._last_generation_summary = self._create_generation_summary(
            population, generation_time, {
                "size": size,
                "age_distribution": age_distribution,
                "gender_distribution": gender_distribution,
                "pathway_distribution": pathway_distribution,
                "birth_year_base": birth_year_base
            }
        )
        
        return population
    
    def _validate_generation_parameters(
        self,
        size: int,
        age_distribution: Dict[str, Any],
        gender_distribution: Dict[str, float],
        pathway_distribution: Optional[Dict[str, float]]
    ) -> None:
        """验证生成参数"""
        # 验证人群规模
        if not isinstance(size, int) or size <= 0:
            raise ParameterValidationError(
                "人群规模必须是正整数",
                "size",
                size
            )
        
        if size > 10_000_000:
            raise ParameterValidationError(
                "人群规模不能超过1000万",
                "size", 
                size
            )
        
        # 验证年龄分布参数
        self._validate_age_distribution(age_distribution)
        
        # 验证性别分布参数
        self._validate_gender_distribution(gender_distribution)
        
        # 验证疾病通路分布参数
        if pathway_distribution:
            self._validate_pathway_distribution(pathway_distribution)
    
    def _validate_age_distribution(self, age_dist: Dict[str, Any]) -> None:
        """验证年龄分布参数"""
        if not isinstance(age_dist, dict):
            raise ParameterValidationError(
                "年龄分布配置必须是字典类型",
                "age_distribution",
                age_dist
            )
        
        dist_type = age_dist.get("type", "normal")
        if dist_type not in ["normal", "uniform", "custom"]:
            raise ParameterValidationError(
                "年龄分布类型必须是 'normal', 'uniform' 或 'custom'",
                "age_distribution.type",
                dist_type
            )
        
        # 验证年龄范围
        min_age = age_dist.get("min_age", 18)
        max_age = age_dist.get("max_age", 100)
        
        validate_age(min_age, "min_age")
        validate_age(max_age, "max_age")
        
        if min_age > max_age:
            raise ParameterValidationError(
                "最小年龄必须小于或等于最大年龄",
                "age_distribution",
                {"min_age": min_age, "max_age": max_age}
            )
        
        # 根据分布类型验证特定参数
        if dist_type == "normal":
            mean = age_dist.get("mean")
            std = age_dist.get("std")
            
            if mean is None or std is None:
                raise ParameterValidationError(
                    "正态分布需要指定 'mean' 和 'std' 参数",
                    "age_distribution",
                    age_dist
                )
            
            validate_age(mean, "mean")
            validate_positive_number(std, "std")
            
            if not (min_age <= mean <= max_age):
                raise ParameterValidationError(
                    "年龄均值必须在最小年龄和最大年龄之间",
                    "age_distribution.mean",
                    mean
                )
    
    def _validate_gender_distribution(self, gender_dist: Dict[str, float]) -> None:
        """验证性别分布参数"""
        if not isinstance(gender_dist, dict):
            raise ParameterValidationError(
                "性别分布配置必须是字典类型",
                "gender_distribution",
                gender_dist
            )
        
        male_ratio = gender_dist.get("male_ratio")
        female_ratio = gender_dist.get("female_ratio")
        
        if male_ratio is None or female_ratio is None:
            raise ParameterValidationError(
                "性别分布必须包含 'male_ratio' 和 'female_ratio'",
                "gender_distribution",
                gender_dist
            )
        
        validate_probability(male_ratio, "male_ratio")
        validate_probability(female_ratio, "female_ratio")
        
        total_ratio = male_ratio + female_ratio
        if abs(total_ratio - 1.0) > 1e-6:
            raise ParameterValidationError(
                "男女比例之和必须等于1.0",
                "gender_distribution",
                {"male_ratio": male_ratio, "female_ratio": female_ratio, "sum": total_ratio}
            )
    
    def _validate_pathway_distribution(self, pathway_dist: Dict[str, float]) -> None:
        """验证疾病通路分布参数"""
        if not isinstance(pathway_dist, dict):
            raise ParameterValidationError(
                "疾病通路分布配置必须是字典类型",
                "pathway_distribution",
                pathway_dist
            )
        
        adenoma_ratio = pathway_dist.get("adenoma_carcinoma_ratio", 0)
        serrated_ratio = pathway_dist.get("serrated_adenoma_ratio", 0)
        
        validate_probability(adenoma_ratio, "adenoma_carcinoma_ratio")
        validate_probability(serrated_ratio, "serrated_adenoma_ratio")
        
        total_ratio = adenoma_ratio + serrated_ratio
        if abs(total_ratio - 1.0) > 1e-6:
            raise ParameterValidationError(
                "疾病通路比例之和必须等于1.0",
                "pathway_distribution",
                {"adenoma_carcinoma_ratio": adenoma_ratio, "serrated_adenoma_ratio": serrated_ratio, "sum": total_ratio}
            )
    
    def _generate_age_distribution(
        self, 
        size: int, 
        age_dist: Dict[str, Any]
    ) -> np.ndarray:
        """生成年龄分布"""
        dist_type = age_dist.get("type", "normal")
        min_age = age_dist.get("min_age", 18)
        max_age = age_dist.get("max_age", 100)
        
        if dist_type == "normal":
            mean = age_dist["mean"]
            std = age_dist["std"]
            
            # 生成正态分布年龄，并截断到指定范围
            ages = np.random.normal(mean, std, size)
            ages = np.clip(ages, min_age, max_age)
            
        elif dist_type == "uniform":
            # 生成均匀分布年龄
            ages = np.random.uniform(min_age, max_age, size)
            
        elif dist_type == "custom":
            # 自定义分布（这里可以扩展支持更多分布类型）
            ages = np.random.uniform(min_age, max_age, size)
            
        return ages
    
    def _assign_genders(
        self, 
        size: int, 
        gender_dist: Dict[str, float]
    ) -> List[Gender]:
        """分配性别"""
        male_ratio = gender_dist["male_ratio"]
        
        # 生成随机数并根据比例分配性别
        random_values = np.random.random(size)
        genders = [
            Gender.MALE if rv < male_ratio else Gender.FEMALE
            for rv in random_values
        ]
        
        return genders
    
    def _assign_pathway_types(
        self, 
        size: int, 
        pathway_dist: Optional[Dict[str, float]]
    ) -> Optional[List[Optional[PathwayType]]]:
        """分配疾病通路类型"""
        if not pathway_dist:
            return None
        
        adenoma_ratio = pathway_dist.get("adenoma_carcinoma_ratio", 0.85)
        
        # 生成随机数并根据比例分配通路类型
        random_values = np.random.random(size)
        pathway_types = [
            PathwayType.ADENOMA_CARCINOMA if rv < adenoma_ratio 
            else PathwayType.SERRATED_ADENOMA
            for rv in random_values
        ]
        
        return pathway_types
    
    def _create_generation_summary(
        self,
        population: Population,
        generation_time: float,
        config: Dict[str, Any]
    ) -> GenerationSummary:
        """创建生成摘要"""
        stats = population.statistics

        # 计算年龄统计
        ages = [ind.get_current_age() for ind in population]
        if ages:
            age_stats = {
                "mean": float(np.mean(ages)),
                "median": float(np.median(ages)),
                "std": float(np.std(ages)),
                "min": float(np.min(ages)),
                "max": float(np.max(ages))
            }
        else:
            age_stats = {
                "mean": 0.0,
                "median": 0.0,
                "std": 0.0,
                "min": 0.0,
                "max": 0.0
            }
        
        return GenerationSummary(
            total_individuals=population.get_size(),
            generation_time=generation_time,
            age_stats=age_stats,
            gender_distribution=stats.get_gender_distribution(),
            pathway_distribution=stats.get_pathway_distribution(),
            config_used=config
        )
    
    def get_last_generation_summary(self) -> Optional[GenerationSummary]:
        """获取最后一次生成的摘要"""
        return self._last_generation_summary
    
    def generate_batch_populations(
        self,
        configs: List[Dict[str, Any]],
        show_progress: bool = True
    ) -> List[Population]:
        """
        批量生成多个人群
        
        Args:
            configs: 配置列表，每个配置对应一个人群
            show_progress: 是否显示进度条
            
        Returns:
            生成的人群列表
        """
        populations = []
        
        iterator = configs
        if show_progress:
            iterator = tqdm(configs, desc="批量生成人群", unit="个人群")
        
        for config in iterator:
            # 确保不传递重复的show_progress参数
            config_copy = config.copy()
            config_copy.pop('show_progress', None)
            population = self.generate_population(**config_copy, show_progress=False)
            populations.append(population)
        
        return populations

    def generate_population_from_file(
        self,
        file_path: Union[str, Path],
        pathway_distribution: Optional[Dict[str, float]] = None,
        birth_year_base: int = 2025,
        show_progress: bool = True
    ) -> Population:
        """
        从文件生成人群

        支持两种文件格式：
        1. 个体格式：每行代表一个人（age, gender）
        2. 聚合格式：每行代表一个年龄-性别组合的人数（age, gender, number/count）

        重要说明：
        - 无论输入是个体数据还是聚合数据，都会先累加成年龄-性别分布统计
        - 然后基于这些分布重新生成模拟个体，而不是直接使用原始个体记录
        - 这样设计的优势：
          * 保持原始数据的分布特征
          * 生成新的随机个体用于模拟（避免重复使用真实个体）
          * 支持疾病通路的随机分配
          * 可以基于分布扩展到更大的人群规模

        Args:
            file_path: 人群结构文件路径（支持CSV和Excel格式）
            pathway_distribution: 疾病通路分布配置
            birth_year_base: 基准年份，用于计算出生年份
            show_progress: 是否显示进度条

        Returns:
            生成的人群对象

        Raises:
            ValidationError: 文件格式错误或数据验证失败时
            FileNotFoundError: 文件不存在时
        """
        import time
        start_time = time.time()

        # 加载和验证文件数据
        age_distribution, gender_distribution, _ = self._load_population_file(file_path)

        # 验证疾病通路分布参数
        if pathway_distribution:
            self._validate_pathway_distribution(pathway_distribution)

        # 从分布数据生成个体
        individuals = self._generate_individuals_from_distributions(
            age_distribution,
            gender_distribution,
            pathway_distribution,
            birth_year_base,
            show_progress
        )

        # 创建人群对象
        population = Population(individuals)

        # 记录生成摘要
        end_time = time.time()
        self._last_summary = GenerationSummary(
            total_individuals=len(individuals),
            generation_time=end_time - start_time,
            age_stats=self._calculate_age_stats(individuals),
            gender_stats=self._calculate_gender_stats(individuals),
            pathway_stats=self._calculate_pathway_stats(individuals) if pathway_distribution else None
        )

        return population

    def _load_population_file(
        self,
        file_path: Union[str, Path]
    ) -> Tuple[Dict[int, int], Dict[str, int], int]:
        """
        加载人群结构文件

        Returns:
            (age_distribution, gender_distribution, total_size)
        """
        file_path = Path(file_path)

        if not file_path.exists():
            raise FileNotFoundError(f"人群结构文件不存在: {file_path}")

        # 根据文件扩展名加载数据
        if file_path.suffix.lower() in ['.xlsx', '.xls']:
            df = pd.read_excel(file_path)
        elif file_path.suffix.lower() == '.csv':
            # 尝试不同的编码
            for encoding in ['utf-8', 'gbk', 'gb2312']:
                try:
                    df = pd.read_csv(file_path, encoding=encoding)
                    break
                except UnicodeDecodeError:
                    continue
            else:
                raise ValidationError("无法识别文件编码，请确保文件为UTF-8、GBK或GB2312编码")
        else:
            raise ValidationError("不支持的文件格式，请选择Excel或CSV文件")

        # 验证和处理数据
        return self._process_population_file_data(df)

    def _process_population_file_data(
        self,
        df: pd.DataFrame
    ) -> Tuple[Dict[int, int], Dict[str, int], int]:
        """处理人群文件数据"""

        # 验证列名并获取映射
        column_mapping = self._validate_population_file_columns(df)

        # 确定是否为聚合格式
        has_count_column = 'count' in column_mapping

        if has_count_column:
            # 处理聚合数据格式
            return self._process_aggregated_population_data(df, column_mapping)
        else:
            # 处理个体数据格式
            return self._process_individual_population_data(df, column_mapping)

    def _validate_population_file_columns(self, df: pd.DataFrame) -> Dict[str, str]:
        """验证人群文件列名并返回映射"""
        columns = df.columns.str.lower().str.strip()

        # 查找年龄列
        age_column = None
        for col in ['age', '年龄', 'Age', 'AGE']:
            if col.lower() in columns.values:
                matching_indices = columns == col.lower()
                age_column = df.columns[matching_indices].tolist()[0]
                break

        if age_column is None:
            raise ValidationError("未找到年龄列，请确保文件包含 'age' 或 '年龄' 列")

        # 查找性别列
        gender_column = None
        for col in ['gender', 'sex', '性别', 'Gender', 'Sex', 'GENDER', 'SEX']:
            if col.lower() in columns.values:
                matching_indices = columns == col.lower()
                gender_column = df.columns[matching_indices].tolist()[0]
                break

        if gender_column is None:
            raise ValidationError("未找到性别列，请确保文件包含 'gender'、'sex' 或 '性别' 列")

        # 查找人数列（可选）
        count_column = None
        for col in ['number', 'count', 'num', '人数', '数量', 'Number', 'Count', 'NUM', 'COUNT']:
            if col.lower() in columns.values:
                matching_indices = columns == col.lower()
                count_column = df.columns[matching_indices].tolist()[0]
                break

        result = {
            'age': age_column,
            'gender': gender_column
        }

        if count_column is not None:
            result['count'] = count_column

        return result

    def _standardize_gender_values(self, gender_series: pd.Series) -> pd.Series:
        """标准化性别值"""
        gender_mapping = {
            'M': Gender.MALE, 'MALE': Gender.MALE, '男': Gender.MALE, '男性': Gender.MALE,
            'F': Gender.FEMALE, 'FEMALE': Gender.FEMALE, '女': Gender.FEMALE, '女性': Gender.FEMALE
        }

        genders = gender_series.map(gender_mapping)
        invalid_genders = genders.isna().sum()
        if invalid_genders > 0:
            raise ValidationError(f"发现 {invalid_genders} 个无效的性别值，请使用 M/F、男/女 或 Male/Female")

        return genders

    def _process_individual_population_data(
        self,
        df: pd.DataFrame,
        column_mapping: Dict[str, str]
    ) -> Tuple[Dict[int, int], Dict[str, int], int]:
        """
        处理个体数据（每行代表一个人）

        将个体记录累加成年龄-性别分布统计，用于后续的模拟人群生成。
        这样做的好处是：
        1. 保持原始数据的分布特征
        2. 生成新的模拟个体而不是直接使用原始个体
        3. 支持扩展到更大的人群规模
        """

        # 清理数据
        df_clean = df[[column_mapping['age'], column_mapping['gender']]].copy()
        df_clean = df_clean.dropna()

        # 处理年龄数据
        age_col = df_clean[column_mapping['age']]
        try:
            df_clean['age_clean'] = pd.to_numeric(age_col, errors='coerce').astype(int)
        except:
            raise ValidationError("年龄列包含无效数据，请确保年龄为数字")

        # 验证年龄范围
        age_min, age_max = df_clean['age_clean'].min(), df_clean['age_clean'].max()
        if age_min < 0 or age_max > 120:
            raise ValidationError(f"年龄数据超出合理范围 (0-120)，实际范围: {age_min}-{age_max}")

        # 处理性别数据
        gender_col = df_clean[column_mapping['gender']].str.strip().str.upper()
        df_clean['gender_clean'] = self._standardize_gender_values(gender_col)

        # 累加生成年龄分布统计（按年龄统计人数）
        age_distribution = df_clean['age_clean'].value_counts().to_dict()

        # 累加生成性别分布统计（按性别统计人数）
        gender_counts = df_clean['gender_clean'].value_counts()
        gender_distribution = {}
        for gender, count in gender_counts.items():
            gender_distribution[gender.value] = count

        total_size = len(df_clean)

        return age_distribution, gender_distribution, total_size

    def _process_aggregated_population_data(
        self,
        df: pd.DataFrame,
        column_mapping: Dict[str, str]
    ) -> Tuple[Dict[int, int], Dict[str, int], int]:
        """处理聚合数据（年龄、性别、人数）"""

        # 清理数据
        required_cols = [column_mapping['age'], column_mapping['gender'], column_mapping['count']]
        df_clean = df[required_cols].copy()
        df_clean = df_clean.dropna()

        # 处理年龄数据
        try:
            df_clean['age_clean'] = pd.to_numeric(df_clean[column_mapping['age']], errors='coerce').astype(int)
        except:
            raise ValidationError("年龄列包含无效数据，请确保年龄为数字")

        # 验证年龄范围
        age_min, age_max = df_clean['age_clean'].min(), df_clean['age_clean'].max()
        if age_min < 0 or age_max > 120:
            raise ValidationError(f"年龄数据超出合理范围 (0-120)，实际范围: {age_min}-{age_max}")

        # 处理人数数据
        try:
            df_clean['count_clean'] = pd.to_numeric(df_clean[column_mapping['count']], errors='coerce').astype(int)
        except:
            raise ValidationError("人数列包含无效数据，请确保人数为正整数")

        # 验证人数为正数
        if (df_clean['count_clean'] <= 0).any():
            raise ValidationError("人数必须为正整数")

        # 处理性别数据
        gender_col = df_clean[column_mapping['gender']].str.strip().str.upper()
        df_clean['gender_clean'] = self._standardize_gender_values(gender_col)

        # 生成年龄分布（按年龄汇总人数）
        age_distribution = df_clean.groupby('age_clean')['count_clean'].sum().to_dict()

        # 生成性别分布（按性别汇总人数）
        gender_counts = df_clean.groupby('gender_clean')['count_clean'].sum()
        gender_distribution = {}
        for gender, count in gender_counts.items():
            gender_distribution[gender.value] = count

        total_size = df_clean['count_clean'].sum()

        return age_distribution, gender_distribution, total_size

    def _generate_individuals_from_distributions(
        self,
        age_distribution: Dict[int, int],
        gender_distribution: Dict[str, int],
        pathway_distribution: Optional[Dict[str, float]],
        birth_year_base: int,
        show_progress: bool
    ) -> List[Individual]:
        """
        从年龄和性别分布生成个体列表

        注意：无论输入是个体数据还是聚合数据，都会先转换为分布统计，
        然后基于分布重新生成模拟个体，这样可以确保：
        1. 保持原始的年龄-性别分布特征
        2. 生成新的随机个体用于模拟
        3. 支持疾病通路的随机分配
        """

        individuals = []

        # 计算总体性别比例
        total_gender = sum(gender_distribution.values())
        male_ratio = gender_distribution.get('male', 0) / total_gender if total_gender > 0 else 0.5

        # 为每个年龄组生成个体
        progress_bar = tqdm(
            age_distribution.items(),
            desc="基于分布生成模拟个体",
            disable=not show_progress
        )

        for age, count in progress_bar:
            # 为这个年龄组生成指定数量的个体
            for _ in range(count):
                # 根据总体性别比例随机分配性别
                # 这样可以保持整体的性别分布，但每个年龄组内会有随机性
                gender = Gender.MALE if np.random.random() < male_ratio else Gender.FEMALE

                # 计算出生年份
                birth_year = birth_year_base - age

                # 随机分配疾病通路
                pathway = self._assign_pathway(pathway_distribution) if pathway_distribution else PathwayType.NORMAL

                # 创建个体
                individual = Individual(
                    id=str(uuid.uuid4()),
                    birth_year=birth_year,
                    gender=gender,
                    pathway=pathway,
                    disease_state=DiseaseState.NORMAL
                )

                individuals.append(individual)

        return individuals

    def _assign_pathway(self, pathway_distribution: Dict[str, float]) -> PathwayType:
        """根据分布随机分配疾病通路"""
        rand_val = np.random.random()
        cumulative_prob = 0.0

        for pathway_name, probability in pathway_distribution.items():
            cumulative_prob += probability
            if rand_val <= cumulative_prob:
                # 将字符串转换为PathwayType枚举
                try:
                    return PathwayType(pathway_name.lower())
                except ValueError:
                    # 如果字符串不匹配，尝试按名称查找
                    for pathway_type in PathwayType:
                        if pathway_type.name.lower() == pathway_name.lower():
                            return pathway_type
                    # 如果都不匹配，返回默认值
                    return PathwayType.NORMAL

        return PathwayType.NORMAL
